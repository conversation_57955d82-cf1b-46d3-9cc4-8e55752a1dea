package ai.conrain.aigc.platform.service.component;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestItemGroupQuery;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import java.util.List;

/**
 * AB测试项目分组 Service定义
 *
 * <AUTHOR>
 * @version TestItemGroupService.java v 0.1 2024-12-19 01:24:06
 */
public interface TestItemGroupService {

    /**
     * 查询AB测试项目分组对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestItemGroupVO selectById(Integer id);

    /**
     * 删除AB测试项目分组对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加AB测试项目分组对象
     *
     * @param testItemGroup 对象参数
     * @return 返回结果
     */
    TestItemGroupVO insert(TestItemGroupVO testItemGroup);

    /**
     * 修改AB测试项目分组对象
     *
     * @param testItemGroup 对象参数
     */
    void updateByIdSelective(TestItemGroupVO testItemGroup);

    /**
     * 带条件批量查询AB测试项目分组列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestItemGroupVO> queryTestItemGroupList(TestItemGroupQuery query);

    /**
     * 带条件查询AB测试项目分组数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestItemGroupCount(TestItemGroupQuery query);

    /**
     * 带条件分页查询AB测试项目分组
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestItemGroupVO> queryTestItemGroupByPage(TestItemGroupQuery query);

    /**
     * 根据项目id查询带结果额分组列表
     *
     * @param itemId 项目id
     * @return 结果
     */
    List<TestItemGroupVO> queryWithResultByItemId(Integer itemId);

    /**
     * 刷新分组结果
     *
     * @param groupId 分组id
     */
    void refreshScore(Integer groupId);

    /**
     * 更新分组对比参数
     *
     * @param itemId           分组id
     * @param roundsNum        轮数
     * @param comparisonParams 参数信息
     * @param testParams       测试参数
     * @param baseSceneIds     基础场景id
     */
    void updateComparisonParamsByItemId(Integer itemId, Integer roundsNum, List<JSONObject> comparisonParams,
                                        List<CreateTestParams> testParams, List<Integer> baseSceneIds);

    /**
     * 根据测试项id删除分组
     *
     * @param testItemId 测试项id
     */
    void deleteByTestItemId(Integer testItemId);
}