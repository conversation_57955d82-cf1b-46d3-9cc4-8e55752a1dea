/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import lombok.Data;

/**
 * 测试支持的创作参数
 *
 * <AUTHOR>
 * @version : TestSupportCreativeRequest.java, v 0.1 2025/8/14 14:42 renxiao.wu Exp $
 */
@Data
public class TestSupportCreativeRequest implements CreativeRequest {
    private static final long serialVersionUID = 4549731990098464888L;

    /** 测试分组id */
    private Integer testGroupId;

    /** 测试参数 */
    private List<CreateTestParams> testParams;

    /** 测试固化场景id */
    private List<Integer> testSolidifySceneIds;

    /** 测试上下文 */
    private JSONObject testContext;

    /** 业务标识 */
    private String bizTag;
}
