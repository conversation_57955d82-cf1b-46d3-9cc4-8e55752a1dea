package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.nio.file.Paths;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

//【这个类的变量会以json映射传入comfyui流程作为参数，所以变量名不能轻易修改】
@Data
public class LoraTaskParams implements Serializable {
    private static final long serialVersionUID = 8368267910343979214L;

    private Integer originalMaterialId;

    //素材类型：cloth/face
    private String materialType = "cloth";

    private String originalClothName;

    //衣服上传父目录（按环境区分）
    private String uploadRootDirName;

    //衣服子目录
    private String clothSubDir;

    //衣服完整目录
    private String clothDir;

    //模型名（不含扩展名)
    private String loraModelName;

    //lora类型,flux/sdxl，默认为sdxl
    private String loraType;

    //lora结果目录
    private String loraModelDir;

    //服装的男女款式描述（man/woman/child，默认值是woman，解决历史数据为空的情况）
    private String clothStyleType = "woman";

    private String colorDescriptions = "";

    //服装类型
    //上装：upper garment
    //下装：lower garment
    //其他：outfit
    private String clothType = "outfit";

    /** 原始服装类型，@see ClothTypeEnum */
    private String originClothType;

    //水印描述
    private String waterMarkDesc = "";

    /** 图片中是否不展示人脸 */
    private String noshowFace;
    /** 往json中追加信息，格式如：{"scene.composition": ".abc"} */
    private String appendInfo = "";

    /** 水印位置 */
    private String watermarkOrigin;
    /** 水印宽度 */
    private Integer watermarkWidth;
    /** 水印高度 */
    private Integer watermarkHeight;

    //是否需要预处理被裁剪的人脸 Y|N
    private String preprocessCensoredFace;

    //是否多色上传（Y/N）默认为N
    private String multiColors = "N";

    /** 服装明细prompt */
    private String clothDetailsPrompt;

    private String colorNumber;

    //搭配偏好
    private String matchPrefer;

    //抠图关键词，默认为空
    private String cutoutKeyword = "";

    //lora训练次数，默认4000，可以动态指定
    private String maxTrainStep;

    //学习率
    private String lr = "";
    //内容或风格
    private String contentOrStyle = "content";
    private String rank = "";
    private String alpha = "";
    private String dropout = "";
    //lora的分辨率，缺省为1024
    private String resolution = "";
    //抠图时，保存样本的最大尺寸，缺省为1024
    private String imageSize = "";
    //抠图时，保存样本的是否需要扩展为方形，缺省为原始比例，1为方形，其它如0为原始比例
    private String isSquare = "";

    //打标llm prompt
    private String captionPrompt = "";

    //lora激活词（场景训练需要，material type不同时，激活词不同，场景lora激活词是linrun2010,脸的lora激活词是linrun0901）
    private String activateKey = "";

    /** 抠图是否仅进行缩放，使用cutoutType代替 */
    @Deprecated
    private String cutoutOnlyUpscale = "";

    /** 抠图类型 */
    private String cutoutType = "";

    /** 是否是补丁任务抠图 */
    private boolean isPatchCutout = false;

    /** 保留搭配物品，Y/N */
    private String reservedItems = "";

    /** 训练扩展信息 */
    private String trainExtInfo = "";

    /** 训练重复次数 */
    private Integer repeatTimes = null;

    // 是否裁剪放大，用于抠图流程的实验参数，Y/N，缺省为N
    private String cut4ScaleUp = "";

    //抠图源目录，用于场景lora抠图流程（去水印）时使用，流程模板中，如果传了则取这个，否则取${clothDir}
    private String cutoutSourceDir;

    //获取打标源目录，用于场景lora打标时使用，流程模板中，如果传了则取这个，否则取${clothDir}
    private String labelSourceDir;

    /** 使用镜头打标词,Y/N */
    private String useShot = "";

    //抠图模型，缺省为v1的sam_vit_h_cloth，新版为v2的sam_hq_h_cloth
    private String cutoutModel = "sam_vit_h_cloth";

    /** 多色背景抠图 */
    private String bgMultiColor = "N";

    /** 训练底模地址 */
    private String trainBaseModel = "";

    /** 是否来需要抠图mask */
    private boolean cutoutMask = false;

    //获取预处理结果目录
    public String getPrepareViewRetDir() {
        return Paths.get(clothDir, "/views").toString();
    }

    //获取抠图结果目录
    public String getCutoutRetDir() {
        return Paths.get(clothDir, "/cutout").toString();
    }

    //获取打标结果目录
    public String getLabelRetDir() {
        return Paths.get(clothDir, "/label").toString();
    }

    //获取lora结果文件路径
    @JsonIgnore
    public String getLoraModelRetFilePath() {
        //flux使用ai toolkit训练时，会自动添加一个文件名相同的子目录，所以需要拼接
        if (StringUtils.equals(loraType, CommonConstants.FLUX)) {
            return Paths.get(loraModelDir, loraModelName + "/", loraModelName + ".safetensors").toString();
        } else {
            return Paths.get(loraModelDir, loraModelName + ".safetensors").toString();
        }
    }
}