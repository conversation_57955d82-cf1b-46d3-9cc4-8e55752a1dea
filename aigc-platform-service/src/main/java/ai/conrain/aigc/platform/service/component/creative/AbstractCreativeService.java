/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import com.alibaba.fastjson2.JSON;

import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.component.CreativeBatchElementsService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.dispatch.DispatchServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.TaskDispatch;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserAdditionalConfigVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CreativeUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.FreemarkerUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.DEFAULT_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_DESC;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_STYLE_SCENE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SEED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_WORKFLOW_KEY;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;
import static ai.conrain.aigc.platform.service.util.ComfyUIUtils.buildClientId;

/**
 * 创作服务基类
 *
 * <AUTHOR>
 * @version : AbstractCreativeService.java, v 0.1 2024/7/18 14:42 renxiao.wu Exp $
 */
@Slf4j
public abstract class AbstractCreativeService<T extends CreativeRequest> implements CreativeService<T> {
    @Value("${comfyui.file.embedWorkflow}")
    protected Boolean embedWorkflow;
    @Value("${comfyui.file.fileType}")
    protected String fileType;
    @Autowired
    protected SystemConfigService systemConfigService;
    @Autowired
    protected MaterialModelService materialModelService;
    @Lazy
    @Autowired
    protected CreativeBatchService creativeBatchService;
    @Autowired
    protected UserPointService userPointService;
    @Autowired
    protected CreativeElementService creativeElementService;
    @Autowired
    protected CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    protected CreativeTaskService creativeTaskService;
    @Autowired
    protected TaskDispatch creativeTaskDispatch;
    @Autowired
    protected FileDispatch fileDispatch;
    @Autowired
    protected DispatchServiceFactory dispatchServiceFactory;

    @Autowired
    protected ComfyuiWorkflowTemplateService comfyuiWorkflowTemplateService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreativeBatchVO create(T request) throws IOException {
        long startTime = System.currentTimeMillis();
        log.info("[性能监控][{}]创建任务开始，时间戳:{}", getCreativeType(), startTime);

        AssertUtil.assertNotNull(request, ResultCode.PARAM_INVALID, "request is null");

        CreativeTypeEnum creativeType = getCreativeType();

        //1.查询并校验loraId
        MaterialModelVO modelVO = fetchModel(request);
        ModelTypeEnum modelType = modelVO != null ? modelVO.getType() : null;

        //2.校验是否已经存在创作任务进行中，则不允许创建
        //已取消最大并行任务限制

        //3.插入创作记录
        CreativeBatchVO data = buildData(request, modelVO);
        data.setModelType(modelType);
        data = creativeBatchService.insert(data);

        //4.校验并扣除算力点
        RoleTypeEnum role = OperationContextHolder.getRoleType();
        if (RoleTypeEnum.SYSTEM != role
                && !OperationContextHolder.isDistributorRole() //渠道商不需要扣除算力点
                && !OperationContextHolder.isAdmin()
                && !systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY, OperationContextHolder.getOperatorUserId())
                && !otherWithoutDeduction(request)
                && !creativeType.free()) {
            userPointService.consumeByImage(data);
        }

        //5.插入创作元素配置信息
        List<CreativeElementVO> elements = new ArrayList<>();
        ElementConfigKeyEnum[] configKeys = creativeType.getConfigKeys();
        Map<Integer, List<Integer>> configs = getConfigs(request);
        if (ArrayUtils.isNotEmpty(configKeys) && MapUtils.isNotEmpty(configs)) {
            //校验并填充元素信息
            checkAndFillElements(request, elements, modelVO);
            creativeBatchElementsService.batchInsert(data, elements);
            //风格lora场景打标
            if (elements.stream().anyMatch(ElementUtils::isStyleScene)) {
                data.addExtInfo(KEY_IS_STYLE_SCENE, true);
            }
        }

        //手动模式不进行任务调度
        if (!getCreativeType().isManual()) {

            //设置输出路径
            String resultPath = ComfyUIUtils.buildOutputPath(data.getId());
            data.setResultPath(resultPath);

            //6.更新output 字段
            creativeBatchService.updateByIdSelective(data);

            //7.异步执行
            creativeBatchService.syncStatus(data);
        }

        //8.后置处理
        postProcess(data, request);

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        log.info("[性能监控][{}]创建任务完成，总耗时:{}ms，开始时间:{}，结束时间:{}", getCreativeType(), executionTime,
                startTime, endTime);

        return data;
    }

    @Override
    public String buildFlow(CreativeTaskVO task, MaterialModelVO modelVO, List<CreativeElementVO> elements) {
        try {
            String clientId = buildClientId(task.getOperatorId());

            //兼容性处理
            if (modelVO != null && modelVO.getExtInfo(KEY_CFG) == null) {
                modelVO.addExtInfo(KEY_CFG, DEFAULT_CFG);
            }

            if (CollectionUtils.isNotEmpty(elements)) {
                elements.forEach(this::fillAgeDesc);
            }

            Map<String, Object> context = new HashMap<>();
            preParse(task, elements, modelVO, context);

            // 填充用户额外配置信息
            buildUserAdditional(task, context);

            context.put("imageNum", task.getBatchCnt());
            String seed = context.get(KEY_SEED) != null ? context.get(KEY_SEED).toString()
                    : RandomStringUtils.randomNumeric(15);
            context.put(KEY_SEED, seed);
            task.addExtInfo(KEY_SEED, seed);

            CreativeUtils.fillImageSizeFromProportion(context, task.getImageProportion());

            context.put("embedWorkflow", embedWorkflow + "");
            context.put("fileType", fileType);

            context.put("clientId", clientId);
            context.put("outputPath", task.getResultPath());
            context.put("fileNamePrefix", ComfyUIUtils.buildFileNamePrefix(task.getId()));

            String flowKey = getFlowKey(elements, modelVO, task, context);
            task.addExtInfo(KEY_WORKFLOW_KEY, flowKey);
            String flowConfig = fetchComfyuiWorkflowTemplateConfig(task, flowKey, context);

            //下面两行顺序不能改，correctFlow 得在FreemarkerUtils.parse之前调用
            flowConfig = correctFlow(flowConfig, context, task, flowKey);
            log.info("buildFlow,context:{}", context);
            String parse = FreemarkerUtils.parse(flowConfig, context);

            String result = postParse(parse);

            log.info("构建prompt长度为:{},taskId={},batchId={}", StringUtils.length(result), task.getId(),
                    task.getBatchId());
            return result;
        } catch (Exception e) {
            log.error("构建prompt失败，taskId={},batchId={}", task.getId(), task.getBatchId(), e);
            throw new BizException(ResultCode.SYS_ERROR, e, "创作提交失败，构建prompt失败");
        }
    }

    private String fetchComfyuiWorkflowTemplateConfig(CreativeTaskVO task, String flowKey, Map<String, Object> ctx) {
        String flowConfig;
        ComfyuiWorkflowTemplateVO workflowTemplateVO = comfyuiWorkflowTemplateService.getActiveTemplateByKey(flowKey,
                task.getUserId());
        if (workflowTemplateVO != null) {
            log.info("使用模板和版本：{},{}", flowKey, workflowTemplateVO.getVersion());
            flowConfig = workflowTemplateVO.getTemplateData();

            //保存工作流版本信息
            task.setTplInfo(new ComfyuiTplInfo(flowKey, workflowTemplateVO.getVersion(), ctx));
        } else {
            log.error("没有找到模板对应的激活版本，只能使用默认配置，{}", flowKey);
            flowConfig = systemConfigService.queryValueByKey(flowKey);
        }

        AssertUtil.assertNotBlank(flowConfig, ResultCode.SYS_ERROR, "创作提交失败，未找到对应的流程模板:" + flowKey);
        return flowConfig;
    }

    /**
     * 获取模型信息
     *
     * @param request 请求
     * @return 模型信息
     */
    protected MaterialModelVO fetchModel(T request) {
        Integer modelId = getModelId(request);
        MaterialModelVO modelVO = null;

        if (modelId != null) {
            modelVO = materialModelService.selectById(modelId);
            AssertUtil.assertNotNull(modelVO, ResultCode.PARAM_INVALID, "loraId不存在");
            if (!OperationContextHolder.isBackRole()) {
                AssertUtil.assertTrue(
                        StringUtils.equals(modelVO.getStatus(), MaterialModelStatusEnum.ENABLED.getCode()),
                        ResultCode.MATERIAL_MODEL_NOT_ENABLED, "lora状态不可用");
            }
        }
        return modelVO;
    }

    /**
     * 对某些特殊流程进行特殊处理
     *
     * @param flow    流程配置
     * @param context 上下文
     * @param task    任务
     * @param flowKey 流程关键字
     * @return 处理后的流程
     */
    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return flow;
    }

    @Override
    public void fillPreTaskResultKey(CreativeTaskVO creativeTaskVO, CreativeTypeEnum creativeType) {
        log.error("[AbstractCreativeService][fillPreTaskResultKey]当前创作类型尚未配置前置任务结果 Key，类型：{}", creativeType);
    }

    /**
     * 获取流程关键词
     *
     * @param elements 元素列表
     * @param modelVO  模型
     * @param task     任务
     * @param context  上下文
     * @return 流程关键词
     */
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        return getCreativeType().getFlowKey();
    }

    /**
     * 构建创作数据记录
     *
     * @param request 请求
     * @param modelVO 模型
     * @return 创作数据记录
     */
    protected abstract CreativeBatchVO buildData(T request, MaterialModelVO modelVO) throws IOException;

    /**
     * 其他不需要点数的场景
     *
     * @param request 请求
     * @return true不需要点数
     */
    protected boolean otherWithoutDeduction(T request) {
        return false;
    }

    /**
     * 获取元素配置
     *
     * @param request 请求
     * @return 元素配置
     */
    protected Map<Integer, List<Integer>> getConfigs(T request) {
        return null;
    }

    /**
     * 根据id获取元素
     *
     * @param id      元素id
     * @param request 请求
     * @param modelVO 模型
     * @return 元素
     */
    protected CreativeElementVO fetchElementById(Integer id, T request, MaterialModelVO modelVO) {
        return creativeElementService.selectByIdWithChildren(id);
    }

    /**
     * 后置处理
     *
     * @param data    创作数据记录
     * @param request 请求
     */
    protected void postProcess(CreativeBatchVO data, T request) {

    }

    /**
     * 获取模型id，如果不需要时，返回null，默认返回null
     *
     * @return 模型id
     */
    protected Integer getModelId(T request) {
        return null;
    }

    /**
     * 前置处理
     *
     * @param task     任务
     * @param elements 元素
     * @param modelVO  模型信息
     * @param context  参数map
     */
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
    }

    /**
     * 后置处理
     *
     * @param prompt prompt请求
     */
    protected String postParse(String prompt) {
        return prompt;
    }

    /**
     * 构建任务列表
     *
     * @param batch    批次信息
     * @param elements 元素列表
     * @return 任务列表
     */
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        List<CreativeTaskVO> result = new ArrayList<>();

        //每张图生成一个任务
        for (int i = 0; i < batch.getBatchCnt(); i++) {
            //插入任务数据
            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);
            fillTaskExt(target, batch, elements, i);

            CreativeTaskVO data = creativeTaskService.insert(target);
            result.add(data);
        }

        return result;
    }

    /**
     * 填充任务的扩展信息
     *
     * @param target   目标任务
     * @param batch    batch信息
     * @param elements 元素列表
     * @param idx      索引
     */
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
    }


    /**
     * 参数校验豁免规则
     *
     * @param element 元素
     * @param request 请求
     * @return 返回是否豁免
     */
    protected boolean exemption(CreativeElementVO element, T request) {
        return false;
    }

    /**
     * 校验并填充元素
     *
     * @param request  请求
     * @param elements 元素列表
     * @param modelVO  模型
     */
    private void checkAndFillElements(T request, List<CreativeElementVO> elements, MaterialModelVO modelVO) {

        Map<Integer, List<Integer>> configs = getConfigs(request);

        ElementConfigKeyEnum[] configKeys = getCreativeType().getConfigKeys();
        ElementConfigKeyEnum[] validateConfigKeys = getCreativeType().getValidateConfigKeys();

        List<CreativeElementVO> roots = Arrays.stream(configKeys).map(
                e -> creativeElementService.queryRootKey(e.name())).collect(Collectors.toList());

        roots.forEach(e -> {
            if (exemption(e, request)) {
                return;
            }
            if (ArrayUtils.contains(validateConfigKeys, ElementConfigKeyEnum.valueOf(e.getConfigKey()))) {
                AssertUtil.assertTrue(configs.containsKey(e.getId()), ResultCode.PARAM_INVALID,
                        "配置项:" + e.getName() + "，未传入");
            }
            List<Integer> configIds = configs.get(e.getId());
            if (configIds != null) {
                AssertUtil.assertTrue(CollectionUtils.isNotEmpty(configIds), ResultCode.PARAM_INVALID,
                        "上传的配置id为空" + e.getId());

                configIds.forEach(id -> {
                    CreativeElementVO elementVO = fetchElementById(id, request, modelVO);

                    AssertUtil.assertTrue(StringUtils.equals(elementVO.getConfigKey(), e.getConfigKey()),
                            ResultCode.BIZ_FAIL, "请求参数configKey不一致");

                    //填充年龄描述到模特形象中
                    fillAgeDesc(elementVO);

                    elements.add(elementVO);
                });
            }
        });
    }

    /**
     * 填充年龄描述到模特形象中
     *
     * @param element 元素
     */
    private void fillAgeDesc(CreativeElementVO element) {
        if (element == null || !StringUtils.equals(ElementConfigKeyEnum.FACE.name(), element.getConfigKey())) {
            return;
        }

        ElementUtils.checkFaceComplete(element);

        String ageDesc = element.getExtInfo(KEY_AGE_DESC, String.class);
        if (StringUtils.isBlank(ageDesc) || StringUtils.contains(element.getTags(), ageDesc)) {
            return;
        }
        String split = "";
        if (!StringUtils.endsWith(StringUtils.trim(element.getTags()), ",")) {
            split = ", ";
        }
        element.setTags(element.getTags() + split + ageDesc);
    }

    /**
     * 填充用户额外配置信息
     *
     * @param task    任务信息
     * @param context 上下文内容
     */
    protected void buildUserAdditional(CreativeTaskVO task, Map<String, Object> context) {
        // 获取用户额外配置信息
        UserAdditionalConfigVO userAdditionalConfigVO = systemConfigService.queryAdditionalCustomerRequirements();
        if (userAdditionalConfigVO == null) {
            return;
        }

        // 获取用户id信息
        Integer masterUserId = task.getUserId();
        if (masterUserId == null) {
            log.error("【用户额外配置】用户id为空，无法获取用户额外配置信息");
            return;
        }

        // 1、全局设置用户图片设备信息定制需求
        Map<String, Object> resultMap = getDeviceInfo(userAdditionalConfigVO, masterUserId);

        String jsonStr = JSON.toJSONString(resultMap);

        // 只转义内部的双引号和反斜杠
        String escaped = jsonStr.replace("\\", "\\\\").replace("\"", "\\\"");

        context.put(CommonConstants.KEY_USER_ADDITIONAL_INFO, escaped);
    }

    /**
     * 全局设置用户图片设备信息定制需求
     *
     * @param userAdditionalConfigVO 用户额外配置信息
     * @param masterUserId           用户id
     * @return Map  封装好的结果
     */
    private Map<String, Object> getDeviceInfo(UserAdditionalConfigVO userAdditionalConfigVO, Integer masterUserId) {
        List<String> matchedKeys = new ArrayList<>();
        Map<String, List<Integer>> deviceInfoMap = userAdditionalConfigVO.getDeviceInfo();
        if (deviceInfoMap != null && masterUserId != null) {
            for (Map.Entry<String, List<Integer>> entry : deviceInfoMap.entrySet()) {
                if (entry.getValue() != null && entry.getValue().contains(masterUserId)) {
                    matchedKeys.add(entry.getKey());
                }
            }
        }

        Map<String, Object> resultMap = new HashMap<>();
        // 设置图片设备来源列表
        resultMap.put(CommonConstants.KEY_PICTURE_DEVICE_INFO, matchedKeys.isEmpty() ? null : matchedKeys);
        // 含有多个设备key时，是否为随机设备（默认为false）
        resultMap.put(CommonConstants.KEY_IS_RANDOM_DEVICE, Boolean.FALSE);

        // 返回就诶过
        return resultMap;
    }

}
